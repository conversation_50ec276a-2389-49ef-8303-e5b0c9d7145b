<!-- Modern Brown & Cream Navbar -->
<nav class="navbar navbar-expand-lg fixed-top brown-cream-navbar" id="modernNavbar">
    <div class="container-fluid">
        <!-- Mobile Header -->
        <div class="d-lg-none w-100 d-flex align-items-center justify-content-between">
            <!-- Mobile Menu Toggle -->
            <button class="navbar-toggler brown-cream-toggler" type="button" id="mobileSidebarToggle" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Mobile Logo -->
            <a class="navbar-brand font-julius brown-cream-brand" href="{{ url('/') }}">
                <i class="fas fa-gem me-1"></i>Kanha Fashion Hub
            </a>

            <!-- Mobile Cart -->
            <a href="{{ url('/cart') }}" class="btn btn-sm brown-cream-cart-mobile position-relative">
                <i class="fas fa-shopping-bag"></i>
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger mobile-cart-badge cart-count">
                    {{ $cartCount ?? 0 }}
                </span>
            </a>
        </div>

        <!-- Desktop Logo -->
        <a class="navbar-brand font-julius brown-cream-brand d-none d-lg-block" href="{{ url('/') }}">
            <i class="fas fa-gem me-2"></i>Kanha Fashion Hub
        </a>

        <!-- Desktop Navigation -->
        <div class="collapse navbar-collapse d-none d-lg-flex" id="navbarNav">
            <ul class="navbar-nav mx-auto brown-cream-nav">
                <li class="nav-item">
                    <a class="nav-link brown-cream-link {{ request()->is('/') ? 'active' : '' }}" href="{{ url('/') }}">
                        <span>Home</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link brown-cream-link dropdown-toggle {{ request()->is('collections*') ? 'active' : '' }}"
                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <span>Collections</span>
                    </a>
                    <ul class="dropdown-menu brown-cream-dropdown">
                        @if(isset($globalCategories) && $globalCategories->count() > 0)
                            @foreach($globalCategories as $category)
                                <li>
                                    <a class="dropdown-item brown-cream-dropdown-item" href="{{ url('/collections/' . $category->slug) }}">
                                        <i class="fas fa-gem me-2"></i>{{ $category->name }}
                                    </a>
                                </li>
                            @endforeach
                            <li><hr class="dropdown-divider brown-cream-divider"></li>
                        @endif
                        <li>
                            <a class="dropdown-item brown-cream-dropdown-item fw-medium" href="{{ url('/collections') }}">
                                <i class="fas fa-th-large me-2"></i>View All Collections
                            </a>
                        </li>
                    </ul>
                </li>
                @if(isset($menuPages) && $menuPages->count() > 0)
                    @foreach($menuPages as $page)
                    <li class="nav-item">
                        <a class="nav-link brown-cream-link {{ request()->is('page/'.$page->slug) ? 'active' : '' }}"
                           href="{{ route('page.show', $page->slug) }}">
                            <span>{{ $page->title }}</span>
                        </a>
                    </li>
                    @endforeach
                @else
                    <li class="nav-item">
                        <a class="nav-link brown-cream-link {{ request()->is('about') ? 'active' : '' }}" href="{{ url('/about') }}">
                            <span>About</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link brown-cream-link {{ request()->is('contact') ? 'active' : '' }}" href="{{ url('/contact') }}">
                            <span>Contact</span>
                        </a>
                    </li>
                @endif
                <li class="nav-item">
                    <a class="nav-link brown-cream-link {{ request()->is('track-order') ? 'active' : '' }}" href="{{ route('order-tracking.index') }}">
                        <span>Track Order</span>
                    </a>
                </li>
            </ul>

            <!-- Desktop Right Side -->
            <div class="navbar-actions d-flex align-items-center gap-2">
                <!-- Search -->
                <a href="{{ url('/search') }}" class="brown-cream-action-btn" title="Search">
                    <i class="fas fa-search"></i>
                </a>

                <!-- Guest-only experience - no user account needed -->

                <!-- Cart -->
                <a href="{{ url('/cart') }}" class="brown-cream-cart position-relative">
                    <i class="fas fa-shopping-bag" style="font-size: 1rem;"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger cart-count" style="font-size: 0.75rem;">
                        {{ $cartCount ?? 0 }}
                    </span>
                </a>
            </div>
        </div>

    </div>

    <!-- Mobile Sidebar Menu -->
    <div class="mobile-sidebar" id="mobileSidebar">
        <div class="mobile-sidebar-header">
            <div class="sidebar-brand">
                <a class="navbar-brand font-julius brown-cream-brand" href="{{ url('/') }}">
                    <i class="fas fa-gem me-2"></i>Kanha Fashion Hub
                </a>
            </div>
            <button class="mobile-sidebar-close" id="mobileSidebarClose">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="mobile-sidebar-content">
            <ul class="mobile-nav">
                <li class="mobile-nav-item">
                    <a class="mobile-nav-link {{ request()->is('/') ? 'active' : '' }}" href="{{ url('/') }}">
                        <i class="fas fa-home me-3"></i>
                        <span>Home</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a class="mobile-nav-link collections-toggle" href="#" onclick="toggleMobileSubmenu(event, 'mobileCollectionsSubmenu')">
                        <i class="fas fa-gem me-3"></i>
                        <span>Collections</span>
                        <i class="fas fa-chevron-down ms-auto submenu-arrow"></i>
                    </a>
                    <div class="mobile-submenu" id="mobileCollectionsSubmenu">
                        @if(isset($globalCategories) && $globalCategories->count() > 0)
                            @foreach($globalCategories as $category)
                                <a class="mobile-submenu-item" href="{{ url('/collections/' . $category->slug) }}">
                                    <i class="fas fa-gem me-2"></i>{{ $category->name }}
                                </a>
                            @endforeach
                            <hr class="mobile-submenu-divider">
                        @endif
                        <a class="mobile-submenu-item fw-medium" href="{{ url('/collections') }}">
                            <i class="fas fa-th-large me-2"></i>View All Collections
                        </a>
                    </div>
                </li>
                @if(isset($menuPages) && $menuPages->count() > 0)
                    @foreach($menuPages as $page)
                    <li class="mobile-nav-item">
                        <a class="mobile-nav-link {{ request()->is('page/'.$page->slug) ? 'active' : '' }}"
                           href="{{ route('page.show', $page->slug) }}">
                            <i class="fas fa-{{ $page->slug === 'about' ? 'info-circle' : ($page->slug === 'contact' ? 'envelope' : 'file-alt') }} me-3"></i>
                            <span>{{ $page->title }}</span>
                        </a>
                    </li>
                    @endforeach
                @else
                    <li class="mobile-nav-item">
                        <a class="mobile-nav-link {{ request()->is('about') ? 'active' : '' }}" href="{{ url('/about') }}">
                            <i class="fas fa-info-circle me-3"></i>
                            <span>About</span>
                        </a>
                    </li>
                    <li class="mobile-nav-item">
                        <a class="mobile-nav-link {{ request()->is('contact') ? 'active' : '' }}" href="{{ url('/contact') }}">
                            <i class="fas fa-envelope me-3"></i>
                            <span>Contact</span>
                        </a>
                    </li>
                @endif
                <li class="mobile-nav-item">
                    <a class="mobile-nav-link {{ request()->is('track-order') ? 'active' : '' }}" href="{{ route('order-tracking.index') }}">
                        <i class="fas fa-shipping-fast me-3"></i>
                        <span>Track Order</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a class="mobile-nav-link" href="{{ url('/search') }}">
                        <i class="fas fa-search me-3"></i>
                        <span>Search</span>
                    </a>
                </li>

                <!-- Guest-only experience - no authentication needed -->
            </ul>
        </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div class="mobile-sidebar-overlay" id="mobileSidebarOverlay"></div>
    </div>
</nav>

<style>
/* ===== PINK & GOLD NAVBAR THEME ===== */
:root {
    --pink-primary: #E91E63;
    --pink-secondary: #F06292;
    --pink-dark: #C2185B;
    --pink-light: #F8BBD9;
    --gold-primary: #FFD700;
    --gold-secondary: #FFC107;
    --gold-warm: #FFF9C4;
    --gold-accent: #FF8F00;
}

/* Main Navbar Container */
.brown-cream-navbar {
    background: linear-gradient(135deg, var(--pink-primary), var(--pink-secondary)) !important;
    min-height: 70px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(233, 30, 99, 0.2);
    border: none;
    overflow: visible !important;
}

.brown-cream-navbar .container-fluid {
    overflow: visible !important;
}

.brown-cream-navbar.scrolled {
    background: linear-gradient(135deg, rgba(233, 30, 99, 0.95), rgba(240, 98, 146, 0.95)) !important;
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 30px rgba(233, 30, 99, 0.3);
}

/* Navbar Brand */
.brown-cream-brand {
    font-weight: 700;
    color: white !important;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-size: 1.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.brown-cream-brand:hover {
    color: var(--gold-warm) !important;
    transform: scale(1.05);
    text-shadow: 0 2px 15px rgba(255, 215, 0, 0.4);
}

.brown-cream-brand i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
    color: var(--gold-primary);
}

/* Navbar Toggler */
.brown-cream-toggler {
    border: 2px solid white;
    border-radius: 8px;
    padding: 0.5rem;
    background: transparent;
    transition: all 0.3s ease;
}

.brown-cream-toggler:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: var(--gold-warm);
}

.brown-cream-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
    border-color: var(--gold-warm);
}

.brown-cream-toggler .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5rem;
    height: 1.5rem;
}

/* Cart Button */
.brown-cream-cart {
    background: linear-gradient(135deg, var(--gold-primary), var(--gold-secondary));
    color: var(--pink-dark);
    border: none;
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
    font-size: 0.9rem;
}

.brown-cream-cart:hover {
    background: linear-gradient(135deg, var(--gold-accent), var(--gold-primary));
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

/* Mobile Cart Button - Override Bootstrap btn classes */
.btn.btn-sm.brown-cream-cart-mobile,
.brown-cream-cart-mobile.btn,
.brown-cream-cart-mobile {
    background: linear-gradient(135deg, var(--gold-primary), var(--gold-secondary)) !important;
    background-image: linear-gradient(135deg, var(--gold-primary), var(--gold-secondary)) !important;
    color: var(--pink-dark) !important;
    border: 1px solid var(--gold-primary) !important;
    border-radius: 6px !important;
    padding: 0.35rem 0.55rem !important;
    margin: 0 !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    font-family: inherit !important;
    box-shadow: 0 2px 8px rgba(245, 245, 220, 0.2) !important;
    font-size: 0.75rem !important;
    line-height: 1 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 38px !important;
    height: 34px !important;
    vertical-align: middle !important;
    white-space: nowrap !important;
    user-select: none !important;
    outline: none !important;
}

.btn.btn-sm.brown-cream-cart-mobile:hover,
.brown-cream-cart-mobile.btn:hover,
.brown-cream-cart-mobile:hover {
    background: linear-gradient(135deg, var(--gold-accent), var(--gold-primary)) !important;
    background-image: linear-gradient(135deg, var(--gold-accent), var(--gold-primary)) !important;
    color: white !important;
    border-color: var(--gold-accent) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4) !important;
    text-decoration: none !important;
}

.btn.btn-sm.brown-cream-cart-mobile:focus,
.brown-cream-cart-mobile.btn:focus,
.brown-cream-cart-mobile:focus {
    background-color: var(--cream-warm) !important;
    background-image: none !important;
    color: var(--brown-dark) !important;
    border-color: var(--cream-warm) !important;
    box-shadow: 0 0 0 0.2rem rgba(139, 69, 19, 0.25) !important;
    text-decoration: none !important;
    outline: none !important;
}

.btn.btn-sm.brown-cream-cart-mobile:active,
.brown-cream-cart-mobile.btn:active,
.brown-cream-cart-mobile:active {
    background-color: var(--cream-warm) !important;
    background-image: none !important;
    color: var(--brown-dark) !important;
    border-color: var(--cream-warm) !important;
    transform: translateY(0) !important;
    box-shadow: 0 2px 8px rgba(245, 245, 220, 0.2) !important;
}

/* Ensure the icon inside mobile cart button is properly sized */
.brown-cream-cart-mobile i {
    font-size: 0.8rem !important;
    color: inherit !important;
}

/* Simple Mobile Cart Badge */
.mobile-cart-badge {
    font-size: 9px !important;
    padding: 1px 3px !important;
    min-width: 14px !important;
    height: 14px !important;
    line-height: 12px !important;
    border-radius: 7px !important;
}

/* Action Buttons */
.brown-cream-action-btn {
    background: transparent;
    border: 2px solid white;
    color: white;
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    font-weight: 500;
}

.brown-cream-action-btn:hover {
    background: linear-gradient(135deg, var(--gold-primary), var(--gold-secondary));
    color: var(--pink-dark);
    border-color: var(--gold-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

/* Hide old mobile menu styles - now using sidebar */
@media (max-width: 991.98px) {
    /* Hide desktop elements completely on mobile */
    #navbarNav {
        display: none !important;
    }
}

/* ===== MOBILE SIDEBAR NAVIGATION ===== */

/* Mobile Sidebar */
.mobile-sidebar {
    position: fixed;
    top: 0;
    left: -100%;
    width: 280px;
    height: 100vh;
    background: linear-gradient(135deg, var(--gold-warm), #FFF);
    box-shadow: 2px 0 20px rgba(233, 30, 99, 0.15);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1050;
    overflow-y: auto;
    overflow-x: hidden;
}

.mobile-sidebar.show {
    left: 0;
}

/* Mobile Sidebar Overlay */
.mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Mobile Sidebar Header */
.mobile-sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, var(--pink-primary), var(--pink-secondary));
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.mobile-sidebar-close {
    background: transparent;
    border: none;
    color: white;
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-sidebar-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

/* Mobile Sidebar Content */
.mobile-sidebar-content {
    padding: 1rem 0;
}

.mobile-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-nav-item {
    margin: 0;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: var(--pink-primary) !important;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
    border: none;
    background: transparent;
    width: 100%;
}

.mobile-nav-link:hover {
    background: linear-gradient(135deg, rgba(233, 30, 99, 0.1), rgba(255, 215, 0, 0.1));
    color: var(--pink-dark) !important;
    transform: translateX(5px);
}

.mobile-nav-link.active {
    background: linear-gradient(135deg, rgba(233, 30, 99, 0.15), rgba(255, 215, 0, 0.15));
    color: var(--pink-dark) !important;
    font-weight: 600;
    border-right: 3px solid var(--pink-primary);
}

.mobile-nav-link i {
    width: 20px;
    text-align: center;
    color: var(--pink-primary);
}

.submenu-arrow {
    transition: transform 0.3s ease;
}

.submenu-arrow.rotated {
    transform: rotate(180deg);
}

/* Mobile Submenu */
.mobile-submenu {
    background: rgba(255, 248, 220, 0.8);
    margin: 0;
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.mobile-submenu.show {
    max-height: 500px;
    padding: 0.5rem 0;
}

.mobile-submenu-item {
    display: block;
    padding: 0.75rem 3rem;
    color: var(--brown-primary) !important;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.mobile-submenu-item:hover {
    background-color: rgba(139, 69, 19, 0.1);
    color: var(--brown-dark) !important;
    transform: translateX(5px);
}

.mobile-submenu-divider {
    border-color: rgba(139, 69, 19, 0.2);
    margin: 0.5rem 2rem;
}

.mobile-nav-divider {
    border-color: rgba(139, 69, 19, 0.2);
    margin: 1rem 1.5rem;
}

/* Desktop Styles */
@media (min-width: 992px) {
    /* Hide mobile elements completely on desktop */
    #mobileMenu {
        display: none !important;
    }

    /* Show desktop elements */
    #desktopNav {
        display: flex !important;
    }

    /* Ensure proper navbar layout */
    .navbar {
        padding: 0.5rem 1rem;
    }

    .navbar-brand {
        margin-right: 1rem;
        font-size: 1.5rem;
    }

    .navbar-nav {
        flex-direction: row;
    }

    /* Override default Bootstrap navbar styles for pink-gold theme */
    .brown-cream-nav .brown-cream-link {
        position: relative;
        padding: 0.75rem 1.25rem;
        margin: 0 0.25rem;
        color: white !important;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        border-radius: 8px;
        overflow: hidden;
    }

    .brown-cream-nav .brown-cream-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s;
    }

    .brown-cream-nav .brown-cream-link:hover::before {
        left: 100%;
    }

    .brown-cream-nav .brown-cream-link:hover {
        color: var(--gold-warm) !important;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 215, 0, 0.1));
        transform: translateY(-2px);
        text-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
    }

    .brown-cream-nav .brown-cream-link.active {
        color: var(--gold-warm) !important;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 215, 0, 0.15));
        font-weight: 600;
        box-shadow: 0 2px 10px rgba(255, 215, 0, 0.2);
    }

    /* Brown & Cream Enhanced Dropdown Styles */
    .brown-cream-dropdown,
    .dropdown-menu {
        background: white !important;
        border: none !important;
        border-radius: 12px !important;
        box-shadow: 0 10px 40px rgba(139, 69, 19, 0.15) !important;
        margin-top: 0.5rem !important;
        padding: 0.75rem 0 !important;
        min-width: 220px !important;
        animation: dropdownFadeIn 0.3s ease !important;
        z-index: 1050 !important;
        position: absolute !important;
    }

    @keyframes dropdownFadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .brown-cream-dropdown-item,
    .dropdown-item {
        padding: 0.75rem 1.25rem !important;
        transition: all 0.3s ease !important;
        color: #495057 !important;
        font-weight: 500 !important;
        border-radius: 8px !important;
        margin: 0 0.5rem !important;
        text-decoration: none !important;
    }

    .brown-cream-dropdown-item:hover,
    .dropdown-item:hover {
        background: linear-gradient(135deg, rgba(233, 30, 99, 0.1), rgba(255, 215, 0, 0.1)) !important;
        color: var(--pink-primary) !important;
        transform: translateX(5px) !important;
    }

    .brown-cream-dropdown-item i,
    .dropdown-item i {
        width: 20px !important;
        text-align: center !important;
        color: var(--pink-primary) !important;
    }

    .brown-cream-header,
    .dropdown-header {
        color: var(--pink-primary) !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .brown-cream-divider,
    .dropdown-divider {
        border-color: rgba(233, 30, 99, 0.2) !important;
        margin: 0.5rem 0 !important;
    }

    /* Right side buttons */
    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    /* Ensure dropdowns work properly */
    .dropdown-toggle::after {
        margin-left: 0.5rem;
    }
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 575.98px) {
    .brown-cream-brand {
        font-size: 1.25rem;
    }

    .brown-cream-cart, .brown-cream-action-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.875rem;
    }

    .btn.btn-sm.brown-cream-cart-mobile,
    .brown-cream-cart-mobile.btn,
    .brown-cream-cart-mobile {
        padding: 0.3rem 0.5rem !important;
        font-size: 0.7rem !important;
        min-width: 36px !important;
        height: 32px !important;
    }

    .brown-cream-cart-mobile i {
        font-size: 0.75rem !important;
    }

    .mobile-cart-badge {
        font-size: 8px !important;
        padding: 1px 2px !important;
        min-width: 12px !important;
        height: 12px !important;
        line-height: 10px !important;
        border-radius: 6px !important;
    }

    .mobile-sidebar {
        width: 260px;
    }

    .mobile-nav-link {
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
    }

    .mobile-submenu-item {
        padding: 0.625rem 2.5rem;
        font-size: 0.9rem;
    }
}

/* ===== ACCESSIBILITY & ENHANCEMENTS ===== */

/* Accessibility for reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .brown-cream-link {
        border: 1px solid currentColor;
    }

    .brown-cream-dropdown-item {
        border: 1px solid transparent;
    }

    .brown-cream-dropdown-item:hover {
        border-color: currentColor;
    }
}

/* Focus states for accessibility */
.brown-cream-link:focus,
.brown-cream-dropdown-item:focus,
.brown-cream-mobile-action:focus,
.brown-cream-cart:focus,
.brown-cream-action-btn:focus,
.brown-cream-mobile-link:focus {
    outline: 2px solid var(--brown-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(139, 69, 19, 0.25);
}

/* Enhanced scroll effect for brown-cream navbar */
.brown-cream-navbar.scrolled {
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.95), rgba(160, 82, 45, 0.95)) !important;
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 30px rgba(139, 69, 19, 0.3);
}

/* Ensure proper z-index for dropdowns */
.brown-cream-dropdown {
    z-index: 1050;
}

/* Smooth transitions for all brown-cream elements */
.brown-cream-brand,
.brown-cream-link,
.brown-cream-dropdown-item,
.brown-cream-mobile-link,
.brown-cream-mobile-action,
.brown-cream-cart,
.brown-cream-action-btn {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
</style>

<script>
// Mobile Submenu Toggle Function
function toggleMobileSubmenu(event, submenuId) {
    event.preventDefault();
    const submenu = document.getElementById(submenuId);
    const arrow = event.currentTarget.querySelector('.submenu-arrow');

    if (submenu.classList.contains('show')) {
        submenu.classList.remove('show');
        arrow.classList.remove('rotated');
    } else {
        // Close all other submenus
        document.querySelectorAll('.mobile-submenu.show').forEach(menu => {
            menu.classList.remove('show');
        });
        document.querySelectorAll('.submenu-arrow.rotated').forEach(arr => {
            arr.classList.remove('rotated');
        });

        // Open this submenu
        submenu.classList.add('show');
        arrow.classList.add('rotated');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.getElementById('modernNavbar');
    const mobileSidebar = document.getElementById('mobileSidebar');
    const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
    const mobileSidebarClose = document.getElementById('mobileSidebarClose');
    const mobileSidebarOverlay = document.getElementById('mobileSidebarOverlay');

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Mobile Sidebar Toggle
    function openMobileSidebar() {
        mobileSidebar.classList.add('show');
        mobileSidebarOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    function closeMobileSidebar() {
        mobileSidebar.classList.remove('show');
        mobileSidebarOverlay.classList.remove('show');
        document.body.style.overflow = '';

        // Close all submenus when closing sidebar
        document.querySelectorAll('.mobile-submenu.show').forEach(menu => {
            menu.classList.remove('show');
        });
        document.querySelectorAll('.submenu-arrow.rotated').forEach(arrow => {
            arrow.classList.remove('rotated');
        });
    }

    // Event listeners for mobile sidebar
    if (mobileSidebarToggle) {
        mobileSidebarToggle.addEventListener('click', openMobileSidebar);
    }

    if (mobileSidebarClose) {
        mobileSidebarClose.addEventListener('click', closeMobileSidebar);
    }

    if (mobileSidebarOverlay) {
        mobileSidebarOverlay.addEventListener('click', closeMobileSidebar);
    }

    // Close sidebar when clicking on navigation links (except submenu toggles)
    document.querySelectorAll('.mobile-nav-link:not(.collections-toggle)').forEach(link => {
        link.addEventListener('click', function() {
            if (!this.classList.contains('collections-toggle')) {
                closeMobileSidebar();
            }
        });
    });

    // Handle escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileSidebar.classList.contains('show')) {
            closeMobileSidebar();
        }
    });

    // No logout handling needed for guest-only experience

    // Initialize Bootstrap dropdowns for brown-cream navbar
    const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl, {
            autoClose: true,
            boundary: 'viewport'
        });
    });

    // Enhanced dropdown behavior for desktop hover (brown-cream theme)
    if (window.innerWidth >= 992) {
        dropdownElementList.forEach(function(dropdownToggle) {
            const dropdownMenu = dropdownToggle.nextElementSibling;

            // Click to toggle dropdown
            dropdownToggle.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
                dropdown.toggle();
            });

            // Hover to show dropdown on desktop
            dropdownToggle.addEventListener('mouseenter', function() {
                if (window.innerWidth >= 992) {
                    const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
                    dropdown.show();
                }
            });

            // Hide dropdown when mouse leaves the dropdown area
            if (dropdownMenu) {
                const dropdownContainer = dropdownToggle.parentElement;
                dropdownContainer.addEventListener('mouseleave', function() {
                    if (window.innerWidth >= 992) {
                        const dropdown = bootstrap.Dropdown.getInstance(dropdownToggle);
                        if (dropdown) {
                            setTimeout(() => {
                                dropdown.hide();
                            }, 100);
                        }
                    }
                });
            }
        });
    }

    // Handle window resize - close sidebar on desktop
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992 && mobileSidebar && mobileSidebar.classList.contains('show')) {
            closeMobileSidebar();
        }
    });
});
</script>