@extends('layouts.app')

@section('title', 'Contact Us - Kanha Fashion Hub')
@section('description', 'Get in touch with Kanha Fashion Hub. Visit our store, call us, or send us a message for any inquiries about our fashion jewelry collection.')

@section('content')
<!-- <PERSON> Header -->
<section class="py-5 bg-gradient-pink text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="font-playfair display-4 fw-bold mb-4">Contact Us</h1>
                <p class="lead mb-4">
                    We'd love to hear from you. Get in touch with our team for any questions
                    about our fashion jewelry collection, custom designs, or services.
                </p>
                <div class="d-flex gap-4">
                    <div class="text-center">
                        <i class="fas fa-phone fs-2 mb-2"></i>
                        <div>Call Us</div>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-envelope fs-2 mb-2"></i>
                        <div>Email Us</div>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-map-marker-alt fs-2 mb-2"></i>
                        <div>Visit Store</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                     alt="Contact Kanha Fashion Hub" class="img-fluid rounded-4 shadow-lg">
            </div>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="py-5">
    <div class="container">
        <div class="row g-4 mb-5">
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm h-100 text-center">
                    <div class="card-body p-4">
                        <div class="bg-primary-pink text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-map-marker-alt fs-2"></i>
                        </div>
                        <h5 class="font-playfair mb-3">Visit Our Store</h5>
                        <p class="text-muted mb-3">
                            123 Fashion Street, Bandra West<br>
                            Mumbai, Maharashtra 400050<br>
                            India
                        </p>
                        <button class="btn btn-outline-primary">Get Directions</button>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm h-100 text-center">
                    <div class="card-body p-4">
                        <div class="bg-primary-pink text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-phone fs-2"></i>
                        </div>
                        <h5 class="font-playfair mb-3">Call Us</h5>
                        <p class="text-muted mb-3">
                            <strong>Main Store:</strong> +91 98765 43210<br>
                            <strong>Customer Care:</strong> +91 98765 43211<br>
                            <strong>WhatsApp:</strong> +91 98765 43212
                        </p>
                        <button class="btn btn-outline-primary">Call Now</button>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm h-100 text-center">
                    <div class="card-body p-4">
                        <div class="bg-primary-pink text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-envelope fs-2"></i>
                        </div>
                        <h5 class="font-playfair mb-3">Email Us</h5>
                        <p class="text-muted mb-3">
                            <strong>General:</strong> <EMAIL><br>
                            <strong>Support:</strong> <EMAIL><br>
                            <strong>Custom Orders:</strong> <EMAIL>
                        </p>
                        <button class="btn btn-outline-primary">Send Email</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Store Hours -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light text-center">
                        <h5 class="font-playfair mb-0">Store Hours</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between">
                                    <span>Monday - Friday</span>
                                    <strong>10:00 AM - 8:00 PM</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Saturday</span>
                                    <strong>10:00 AM - 9:00 PM</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Sunday</span>
                                    <strong>11:00 AM - 7:00 PM</strong>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between">
                                    <span>Public Holidays</span>
                                    <strong>11:00 AM - 6:00 PM</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Festival Days</span>
                                    <strong>Special Hours</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Emergency</span>
                                    <strong>By Appointment</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-5">
                    <h2 class="section-title">Send Us a Message</h2>
                    <p class="lead text-muted">Have a question or need assistance? We're here to help!</p>
                </div>
                
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-5">
                        <form id="contactForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="firstName" class="form-label">First Name *</label>
                                    <input type="text" class="form-control form-control-lg" id="firstName" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="lastName" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control form-control-lg" id="lastName" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control form-control-lg" id="email" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control form-control-lg" id="phone">
                                </div>
                                <div class="col-12">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <select class="form-select form-select-lg" id="subject" required>
                                        <option value="">Select a subject</option>
                                        <option value="general">General Inquiry</option>
                                        <option value="product">Product Information</option>
                                        <option value="custom">Custom Design</option>
                                        <option value="repair">Repair Services</option>
                                        <option value="complaint">Complaint</option>
                                        <option value="feedback">Feedback</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control form-control-lg" id="message" rows="5" 
                                              placeholder="Please describe your inquiry in detail..." required></textarea>
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="newsletter">
                                        <label class="form-check-label" for="newsletter">
                                            Subscribe to our newsletter for updates and exclusive offers
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="privacy" required>
                                        <label class="form-check-label" for="privacy">
                                            I agree to the <a href="{{ route('privacy-policy') }}" class="text-primary-pink">Privacy Policy</a> 
                                            and consent to being contacted by ShreeJi Jewelry *
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary-pink btn-lg px-5">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Find Our Store</h2>
            <p class="lead text-muted">Located in the heart of Mumbai's jewelry district</p>
        </div>
        
        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <!-- Map Placeholder -->
                <div class="map-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 400px;">
                    <div class="text-center">
                        <i class="fas fa-map-marked-alt text-primary-pink fs-1 mb-3"></i>
                        <h5 class="text-muted">Interactive Map</h5>
                        <p class="text-muted">Google Maps integration would go here</p>
                        <button class="btn btn-primary-pink">View on Google Maps</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <p class="lead text-muted">Quick answers to common questions</p>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item border-0 shadow-sm mb-3">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                What are your store hours?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                We're open Monday-Friday 10AM-8PM, Saturday 10AM-9PM, and Sunday 11AM-7PM. 
                                Holiday hours may vary, so please call ahead during festival seasons.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item border-0 shadow-sm mb-3">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                Do you offer custom jewelry design?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes! We specialize in custom jewelry design. Our master craftsmen can create unique pieces 
                                based on your specifications. Contact us to schedule a consultation.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item border-0 shadow-sm mb-3">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                What is your return policy?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                We offer a 30-day return policy for most items in original condition. Custom pieces 
                                and personalized jewelry may have different terms. Please see our returns page for details.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item border-0 shadow-sm">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                Do you provide jewelry repair services?
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, we offer comprehensive jewelry repair services including resizing, stone replacement, 
                                chain repair, and restoration. Bring your piece to our store for a free assessment.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
    .map-placeholder {
        border: 2px dashed #dee2e6;
        border-radius: 0.375rem;
    }
    
    .accordion-button {
        background-color: var(--light-gray);
        color: var(--text-dark);
        font-weight: 500;
    }
    
    .accordion-button:not(.collapsed) {
        background-color: var(--primary-pink);
        color: white;
    }
    
    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(255, 20, 147, 0.25);
    }
</style>
@endpush

@push('scripts')
<script>
    document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Basic form validation
        const firstName = document.getElementById('firstName').value;
        const lastName = document.getElementById('lastName').value;
        const email = document.getElementById('email').value;
        const subject = document.getElementById('subject').value;
        const message = document.getElementById('message').value;
        const privacy = document.getElementById('privacy').checked;
        
        if (!firstName || !lastName || !email || !subject || !message || !privacy) {
            alert('Please fill in all required fields and accept the privacy policy.');
            return;
        }
        
        // Here you would typically send the form data to your backend
        alert('Thank you for your message! We\'ll get back to you within 24 hours.');
        this.reset();
    });
</script>
@endpush
