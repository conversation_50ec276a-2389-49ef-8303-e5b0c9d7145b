<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin Dashboard') - ShreeJi Jewelry</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom Admin Styles -->
    <style>
        :root {
            /* Pink-Gold Admin Theme */
            --primary-pink: #E91E63;
            --secondary-pink: #F06292;
            --dark-pink: #C2185B;
            --light-pink: #F8BBD9;
            --primary-gold: #FFD700;
            --secondary-gold: #FFC107;
            --dark-gold: #FF8F00;
            --light-gold: #FFF9C4;
            --rose-gold: #E8B4B8;
            --champagne-gold: #F7E7CE;
            --text-dark: #2c3e50;
            --bg-light: #f8f9fa;
            --sidebar-width: 280px;
            --top-navbar-height: 70px;

            /* Legacy compatibility */
            --primary-brown: #E91E63;
            --secondary-brown: #F06292;
            --dark-brown: #C2185B;
            --primary-cream: #FFF9C4;
            --warm-cream: #FFC1CC;
        }

        /* Mobile-First Base Styles */
        body {
            font-family: 'Figtree', sans-serif;
            background-color: var(--bg-light);
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Mobile Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: -100%;
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(135deg, var(--primary-pink), var(--dark-pink));
            box-shadow: 2px 0 20px rgba(233, 30, 99, 0.15);
            transition: all 0.3s ease;
            z-index: 1050;
            overflow-y: auto;
        }

        .sidebar.show {
            left: 0;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1040;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .sidebar .nav-link {
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin: 3px 15px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: var(--light-gold);
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 193, 7, 0.15));
            transform: translateX(5px);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 12px;
            font-size: 1.1rem;
        }

        /* Mobile Top Navbar */
        .top-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            padding: 15px 20px;
            height: var(--top-navbar-height);
            z-index: 1030;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        /* Main Content Mobile */
        .main-content {
            margin-top: var(--top-navbar-height);
            min-height: calc(100vh - var(--top-navbar-height));
            padding: 0;
            width: 100%;
        }

        .content-wrapper {
            padding: 20px 15px;
        }

        /* Mobile Cards */
        .card {
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-radius: 15px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        /* Mobile Buttons */
        .btn {
            border-radius: 10px;
            font-weight: 500;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .btn-primary:hover {
            background-color: var(--secondary-brown);
            border-color: var(--secondary-brown);
            transform: translateY(-1px);
        }

        .btn-primary-pink {
            background-color: var(--primary-pink);
            border-color: var(--primary-pink);
            color: white;
        }

        .btn-primary-pink:hover {
            background-color: var(--secondary-brown);
            border-color: var(--secondary-brown);
            color: white;
            transform: translateY(-1px);
        }

        /* Mobile Typography */
        .text-primary {
            color: var(--primary-pink) !important;
        }

        .font-playfair {
            font-family: 'Playfair Display', serif;
        }

        /* Mobile Border Utilities */
        .border-left-primary {
            border-left: 4px solid var(--primary-pink) !important;
        }

        .border-left-success {
            border-left: 4px solid #28a745 !important;
        }

        .border-left-info {
            border-left: 4px solid #17a2b8 !important;
        }

        .border-left-warning {
            border-left: 4px solid #ffc107 !important;
        }

        /* Mobile Table Responsive */
        .table-responsive {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--text-dark);
            background-color: #f8f9fa;
        }

        /* Mobile Responsive Breakpoints */
        @media (max-width: 575.98px) {
            .top-navbar {
                padding: 10px 15px;
                height: 60px;
            }

            .main-content {
                margin-top: 60px;
            }

            .content-wrapper {
                padding: 15px 10px;
            }

            .card {
                border-radius: 12px;
                margin-bottom: 15px;
            }

            .btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .sidebar {
                width: 100%;
            }
        }

        @media (min-width: 576px) and (max-width: 767.98px) {
            .content-wrapper {
                padding: 20px 15px;
            }
        }

        @media (min-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                width: var(--sidebar-width);
            }

            .main-content {
                margin-left: var(--sidebar-width);
                width: calc(100% - var(--sidebar-width));
            }

            .top-navbar {
                left: var(--sidebar-width);
                width: calc(100% - var(--sidebar-width));
            }

            .content-wrapper {
                padding: 30px;
            }

            .sidebar-toggle {
                display: none;
            }
        }

        @media (min-width: 992px) {
            .content-wrapper {
                padding: 40px;
            }
        }

        /* Mobile Menu Toggle */
        .sidebar-toggle {
            background: none;
            border: 2px solid var(--primary-brown);
            color: var(--primary-brown);
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background-color: var(--primary-brown);
            color: var(--primary-cream);
        }

        /* Mobile Badge Styles */
        .badge {
            border-radius: 20px;
            padding: 6px 12px;
            font-weight: 500;
        }

        /* Mobile Animation */
        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }

        .sidebar.show {
            animation: slideIn 0.3s ease;
        }

        /* Legacy dropdown styles removed - now using collapsible submenu */

        /* Collapsible Submenu Styles */
        .sidebar .submenu-item {
            color: var(--secondary-cream) !important;
            padding: 8px 20px !important;
            margin: 2px 8px !important;
            font-size: 0.9rem !important;
            border-radius: 6px !important;
        }

        .sidebar .submenu-item:hover {
            background-color: rgba(245, 245, 220, 0.1) !important;
            color: var(--warm-cream) !important;
            transform: translateX(3px);
        }

        .sidebar .submenu-item.active {
            background-color: rgba(245, 245, 220, 0.2) !important;
            color: var(--warm-cream) !important;
        }

        /* Chevron rotation for collapsible menu */
        .nav-link[data-bs-toggle="collapse"] .fa-chevron-down {
            transition: transform 0.3s ease;
        }

        .nav-link[data-bs-toggle="collapse"][aria-expanded="true"] .fa-chevron-down {
            transform: rotate(180deg);
        }

        /* Mobile submenu improvements */
        @media (max-width: 767.98px) {
            .submenu-container {
                margin: 5px 10px !important;
            }

            .sidebar .submenu-item {
                padding: 10px 16px !important;
                margin: 2px 6px !important;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Mobile Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="pt-4">
            <!-- Close Button for Mobile -->
            <div class="d-flex justify-content-between align-items-center px-3 mb-3 d-md-none">
                <div>
                    <h4 class="text-white font-playfair mb-0">ShreeJi Admin</h4>
                    <small class="text-white-50">Jewelry Management</small>
                </div>
                <button class="btn btn-link text-white p-0" id="sidebarClose" style="font-size: 1.5rem;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Logo for Desktop -->
            <div class="text-center mb-4 px-3 d-none d-md-block">
                <h4 class="text-white font-playfair mb-1">ShreeJi Admin</h4>
                <small class="text-white-50">Jewelry Management</small>
            </div>

            <!-- Navigation -->
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}"
                       href="{{ route('admin.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.products.*') ? 'active' : '' }}"
                       href="{{ route('admin.products.index') }}">
                        <i class="fas fa-gem"></i>
                        Products
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.categories.*') ? 'active' : '' }}"
                       href="{{ route('admin.categories.index') }}">
                        <i class="fas fa-tags"></i>
                        Categories
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.orders.index') ? 'active' : '' }}"
                       href="{{ route('admin.orders.index') }}">
                        <i class="fas fa-shopping-cart"></i>
                        Orders
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.orders.tracking.*') ? 'active' : '' }}"
                       href="{{ route('admin.orders.tracking.index') }}">
                        <i class="fas fa-truck"></i>
                        Order Tracking
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.customers.*') ? 'active' : '' }}"
                       href="{{ route('admin.customers.index') }}">
                        <i class="fas fa-users"></i>
                        Customers
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.promocodes.*') ? 'active' : '' }}"
                       href="{{ route('admin.promocodes.index') }}">
                        <i class="fas fa-percent"></i>
                        Promocodes
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}"
                       href="{{ route('admin.settings.shipping') }}">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                </li>

                <!-- Pages Menu with Collapsible Submenu -->
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.pages.*') ? 'active' : '' }}"
                       href="#" data-bs-toggle="collapse" data-bs-target="#pagesSubmenu"
                       aria-expanded="{{ request()->routeIs('admin.pages.*') ? 'true' : 'false' }}" aria-controls="pagesSubmenu">
                        <i class="fas fa-file-alt"></i>
                        Pages
                        @php
                            $pageCount = \App\Models\Page::count();
                        @endphp
                        @if($pageCount > 0)
                        <span class="badge bg-light text-dark ms-auto">{{ $pageCount }}</span>
                        @endif
                        <i class="fas fa-chevron-down ms-2" style="font-size: 0.8rem; transition: transform 0.3s ease;"></i>
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.pages.*') ? 'show' : '' }}" id="pagesSubmenu">
                        <div class="submenu-container" style="background: rgba(0,0,0,0.3); margin: 5px 15px; border-radius: 8px; padding: 8px 0;">
                            <a class="nav-link submenu-item {{ request()->routeIs('admin.pages.index') ? 'active' : '' }}"
                               href="{{ route('admin.pages.index') }}" style="padding: 8px 20px; margin: 2px 8px; font-size: 0.9rem;">
                                <i class="fas fa-list me-2"></i>All Pages
                            </a>
                            <a class="nav-link submenu-item {{ request()->routeIs('admin.pages.create') ? 'active' : '' }}"
                               href="{{ route('admin.pages.create') }}" style="padding: 8px 20px; margin: 2px 8px; font-size: 0.9rem;">
                                <i class="fas fa-plus me-2"></i>Create New Page
                            </a>
                            <hr style="border-color: rgba(255,255,255,0.2); margin: 8px 16px;">
                            <a class="nav-link submenu-item"
                               href="{{ route('admin.pages.index') }}?status=published" style="padding: 8px 20px; margin: 2px 8px; font-size: 0.9rem;">
                                <i class="fas fa-eye me-2"></i>Published Pages
                            </a>
                            <a class="nav-link submenu-item"
                               href="{{ route('admin.pages.index') }}?status=draft" style="padding: 8px 20px; margin: 2px 8px; font-size: 0.9rem;">
                                <i class="fas fa-eye-slash me-2"></i>Draft Pages
                            </a>
                            <a class="nav-link submenu-item"
                               href="{{ route('admin.pages.index') }}?template=legal" style="padding: 8px 20px; margin: 2px 8px; font-size: 0.9rem;">
                                <i class="fas fa-gavel me-2"></i>Legal Pages
                            </a>
                        </div>
                    </div>
                </li>

                <hr class="my-3 mx-3" style="border-color: rgba(255,255,255,0.2);">

                <li class="nav-item">
                    <a class="nav-link" href="{{ route('home') }}" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        View Website
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{{ route('admin.profile.show') }}">
                        <i class="fas fa-user"></i>
                        Profile
                    </a>
                </li>

                <li class="nav-item">
                    <form method="POST" action="{{ route('admin.logout') }}" class="d-inline w-100">
                        @csrf
                        <button type="submit" class="nav-link border-0 bg-transparent w-100 text-start">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Top Navbar -->
    <div class="top-navbar">
        <div class="d-flex align-items-center">
            <button class="sidebar-toggle me-3" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            <span class="text-muted d-none d-sm-inline">Welcome back, {{ Auth::user()->name }}!</span>
            <span class="text-muted d-sm-none">{{ Str::limit(Auth::user()->name, 15) }}</span>
        </div>
        <div class="d-flex align-items-center gap-2">
            <span class="badge bg-primary d-none d-sm-inline">{{ ucfirst(Auth::user()->role) }}</span>
            <div class="dropdown">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle"></i>
                    <span class="d-none d-md-inline ms-1">Account</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="{{ route('admin.profile.show') }}">
                        <i class="fas fa-user me-2"></i>Profile
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form method="POST" action="{{ route('admin.logout') }}">
                            @csrf
                            <button type="submit" class="dropdown-item">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-wrapper">
            @yield('content')
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Admin Mobile JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarClose = document.getElementById('sidebarClose');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            // Toggle sidebar
            function toggleSidebar() {
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
                document.body.style.overflow = sidebar.classList.contains('show') ? 'hidden' : '';
            }

            // Close sidebar
            function closeSidebar() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
            }

            // Event listeners
            sidebarToggle.addEventListener('click', toggleSidebar);
            sidebarOverlay.addEventListener('click', closeSidebar);

            // Close button event listener
            if (sidebarClose) {
                sidebarClose.addEventListener('click', closeSidebar);
            }

            // Close sidebar when clicking on nav links (mobile)
            const navLinks = sidebar.querySelectorAll('.nav-link:not([data-bs-toggle="collapse"])');
            const submenuItems = sidebar.querySelectorAll('.submenu-item');

            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    // Don't close sidebar if it's a collapsible toggle
                    if (!link.hasAttribute('data-bs-toggle')) {
                        if (window.innerWidth < 768) {
                            closeSidebar();
                        }
                    }
                });
            });

            // Close sidebar when clicking on submenu items (mobile)
            submenuItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (window.innerWidth < 768) {
                        closeSidebar();
                    }
                });
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768) {
                    closeSidebar();
                }
            });

            // Prevent body scroll when sidebar is open
            sidebar.addEventListener('touchmove', function(e) {
                if (sidebar.classList.contains('show')) {
                    e.preventDefault();
                }
            }, { passive: false });
        });

        // Global admin functions
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Mobile table scroll indicator
        function addTableScrollIndicator() {
            const tables = document.querySelectorAll('.table-responsive');
            tables.forEach(table => {
                if (table.scrollWidth > table.clientWidth) {
                    table.classList.add('has-scroll');

                    // Add scroll indicator
                    const indicator = document.createElement('div');
                    indicator.className = 'scroll-indicator';
                    indicator.innerHTML = '<i class="fas fa-arrow-right"></i> Scroll to see more';
                    table.appendChild(indicator);

                    table.addEventListener('scroll', function() {
                        if (this.scrollLeft > 10) {
                            indicator.style.display = 'none';
                        } else {
                            indicator.style.display = 'block';
                        }
                    });
                }
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', addTableScrollIndicator);
    </script>
    @stack('scripts')
</body>
</html>
