@extends('layouts.app')

@section('title', isset($category) ? $category->name . ' - ShreeJi Jewelry' : 'Collections - ShreeJi Jewelry')
@section('description', isset($category) ? $category->description : 'Browse our complete collection of exquisite jewelry
    including rings, necklaces, earrings, and bracelets.')

@section('content')
    <!-- Page Header -->
    <section class="py-4 py-md-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    @if (isset($category))
                        <h1 class="font-modern display-5 display-md-4 fw-bold mb-2 mb-md-3">{{ $category->name }}</h1>
                        <p class="lead text-muted d-none d-md-block">{{ $category->description }}</p>
                        <p class="text-muted d-md-none">{{ Str::limit($category->description, 80) }}</p>
                    @elseif(isset($searchTerm))
                        <h1 class="font-modern display-5 display-md-4 fw-bold mb-2 mb-md-3">Search Results</h1>
                        <p class="lead text-muted d-none d-md-block">
                            @if ($products->count() > 0)
                                Found {{ $products->total() }} results for "{{ $searchTerm }}"
                            @else
                                No results found for "{{ $searchTerm }}"
                            @endif
                        </p>
                        <p class="text-muted d-md-none">
                            @if ($products->count() > 0)
                                {{ $products->total() }} results for "{{ Str::limit($searchTerm, 20) }}"
                            @else
                                No results found
                            @endif
                        </p>
                    @else
                        <h1 class="font-modern display-5 display-md-4 fw-bold mb-2 mb-md-3">Our Collections</h1>
                        <p class="lead text-muted d-none d-md-block">Discover our complete range of exquisite jewelry pieces
                        </p>
                        <p class="text-muted d-md-none">Exquisite jewelry pieces</p>
                    @endif

                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="d-none d-md-block">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}">Home</a></li>
                            @if (isset($category))
                                <li class="breadcrumb-item"><a href="{{ route('collections') }}">Collections</a></li>
                                <li class="breadcrumb-item active" aria-current="page">{{ $category->name }}</li>
                            @else
                                <li class="breadcrumb-item active" aria-current="page">Collections</li>
                            @endif
                        </ol>
                    </nav>

                    <!-- Mobile Breadcrumb -->
                    <nav aria-label="breadcrumb" class="d-md-none">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ url('/') }}"><i class="fas fa-home"></i></a></li>
                            @if (isset($category))
                                <li class="breadcrumb-item"><a href="{{ route('collections') }}">Collections</a></li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    {{ Str::limit($category->name, 15) }}</li>
                            @else
                                <li class="breadcrumb-item active" aria-current="page">Collections</li>
                            @endif
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="py-3 py-md-4 border-bottom bg-white sticky-top shadow-sm" style="top: var(--navbar-height); z-index: 100;">
        <div class="container">
            <!-- Mobile Filter Layout -->
            <div class="d-md-none">
                <div class="row g-2">
                    <div class="col-4">
                        <div class="dropdown w-100">
                            <button class="btn btn-outline-pink btn-sm dropdown-toggle w-100" type="button"
                                data-bs-toggle="dropdown" style="border-color: var(--primary-pink); color: var(--primary-pink);">
                                <i class="fas fa-filter me-1"></i>Category
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('collections') }}">All Categories</a></li>
                                @if (isset($globalCategories))
                                    @foreach ($globalCategories as $cat)
                                        <li><a class="dropdown-item"
                                                href="{{ route('collections.category', $cat->slug) }}">{{ $cat->name }}</a>
                                        </li>
                                    @endforeach
                                @endif
                            </ul>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="dropdown w-100">
                            <button class="btn btn-outline-pink btn-sm dropdown-toggle w-100" type="button"
                                data-bs-toggle="dropdown" style="border-color: var(--primary-pink); color: var(--primary-pink);">
                                <i class="fas fa-rupee-sign me-1"></i>Price
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => null, 'max_price' => null]) }}">All
                                        Prices</a></li>
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => null, 'max_price' => 10000]) }}">Under
                                        ₹10K</a></li>
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => 10000, 'max_price' => 25000]) }}">₹10K
                                        - ₹25K</a></li>
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => 25000, 'max_price' => 50000]) }}">₹25K
                                        - ₹50K</a></li>
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => 50000, 'max_price' => null]) }}">Above
                                        ₹50K</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-4">
                        <select class="form-select form-select-sm" onchange="window.location.href = this.value"
                                style="border-color: var(--primary-pink); color: var(--primary-pink);">
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'featured']) }}"
                                {{ request('sort') == 'featured' ? 'selected' : '' }}>Featured</option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'price_low']) }}"
                                {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price ↑</option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'price_high']) }}"
                                {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price ↓</option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'newest']) }}"
                                {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest</option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'name']) }}"
                                {{ request('sort') == 'name' ? 'selected' : '' }}>A-Z</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Desktop Filter Layout -->
            <div class="row align-items-center d-none d-md-flex">
                <div class="col-md-6">
                    <div class="d-flex align-items-center gap-3">
                        <span class="fw-semibold" style="color: var(--primary-pink);">Filter by:</span>
                        <div class="dropdown">
                            <button class="btn btn-outline-pink dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" style="border-color: var(--primary-pink); color: var(--primary-pink);">
                                Category
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('collections') }}">All Categories</a></li>
                                @if (isset($globalCategories))
                                    @foreach ($globalCategories as $cat)
                                        <li><a class="dropdown-item"
                                                href="{{ route('collections.category', $cat->slug) }}">{{ $cat->name }}</a>
                                        </li>
                                    @endforeach
                                @endif
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-pink dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" style="border-color: var(--primary-pink); color: var(--primary-pink);">
                                Price Range
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => null, 'max_price' => null]) }}">All
                                        Prices</a></li>
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => null, 'max_price' => 10000]) }}">Under
                                        ₹10,000</a></li>
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => 10000, 'max_price' => 25000]) }}">₹10,000
                                        - ₹25,000</a></li>
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => 25000, 'max_price' => 50000]) }}">₹25,000
                                        - ₹50,000</a></li>
                                <li><a class="dropdown-item"
                                        href="{{ request()->fullUrlWithQuery(['min_price' => 50000, 'max_price' => null]) }}">Above
                                        ₹50,000</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-md-end gap-3">
                        <span class="fw-semibold" style="color: var(--primary-pink);">Sort by:</span>
                        <select class="form-select" style="width: auto; border-color: var(--primary-pink); color: var(--primary-pink);" onchange="window.location.href = this.value">
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'featured']) }}"
                                {{ request('sort') == 'featured' ? 'selected' : '' }}>Featured</option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'price_low']) }}"
                                {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'price_high']) }}"
                                {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'newest']) }}"
                                {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest First</option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'name']) }}"
                                {{ request('sort') == 'name' ? 'selected' : '' }}>Name A-Z</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Grid -->
    <section class="py-5">
        <div class="container">
            @if ($products->count() > 0)
                <div class="row g-4 g-md-4">
                    @foreach ($products as $product)
                        <div class="col-12 col-md-6 col-lg-4 col-xl-3">
                            <div class="card product-card h-100 {{ !$product->in_stock ? 'out-of-stock' : '' }}">
                                <div class="position-relative overflow-hidden product-image-container">
                                    @if($product->images && count($product->images) > 0)
                                        @php
                                            $imagePath = $product->images[0];
                                            // Check if it's a full URL
                                            if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                                                $imageUrl = $imagePath;
                                            } else {
                                                // Clean the path and ensure storage/ prefix
                                                $cleanPath = str_replace(['\\/', '\\'], '/', $imagePath);
                                                // Add storage/ prefix if not already present
                                                if (!str_starts_with($cleanPath, 'storage/')) {
                                                    $cleanPath = 'storage/' . ltrim($cleanPath, '/');
                                                }
                                                $imageUrl = asset($cleanPath);
                                            }
                                        @endphp
                                        <img src="{{ $imageUrl }}"
                                             class="card-img-top {{ !$product->in_stock ? 'opacity-50' : '' }}"
                                             alt="{{ $product->name }}"
                                             style="height: 250px; object-fit: cover;"
                                             onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                                    @else
                                        <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                             class="card-img-top {{ !$product->in_stock ? 'opacity-50' : '' }}"
                                             alt="{{ $product->name }}"
                                             style="height: 250px; object-fit: cover;">
                                    @endif

                                    @if(!$product->in_stock)
                                        <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.3);">
                                            <span class="badge bg-secondary fs-6 px-3 py-2">
                                                <i class="fas fa-times me-1"></i>Out of Stock
                                            </span>
                                        </div>
                                    @endif

                                    <!-- Hover hint for desktop -->
                                    <!-- Removed overlay - keeping only subtle hover effects -->

                                    @if ($product->isOnSale())
                                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                            {{ $product->discount_percentage }}% OFF
                                        </span>
                                    @elseif($product->is_featured)
                                        <span
                                            class="badge position-absolute top-0 start-0 m-2" style="background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink)); color: white;">Featured</span>
                                    @endif
                                </div>
                                <div class="card-body text-center d-flex flex-column">
                                    <h5 class="card-title font-modern">{{ $product->name }}</h5>
                                    <p class="card-text text-muted small">{{ $product->category->name }}</p>
                                    <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                                        @if ($product->isOnSale())
                                            <span
                                                class="fw-bold fs-5 font-modern" style="color: var(--primary-pink);">₹{{ number_format($product->sale_price) }}</span>
                                            <span
                                                class="text-muted text-decoration-line-through small">₹{{ number_format($product->price) }}</span>
                                        @else
                                            <span
                                                class="fw-bold fs-5 font-modern" style="color: var(--primary-pink);">₹{{ number_format($product->price) }}</span>
                                        @endif
                                    </div>
                                    @if ($product->metal_type || $product->stone_type)
                                        <div class="mt-auto mb-3">
                                            @if ($product->metal_type)
                                                <small
                                                    class="badge bg-light text-dark me-1">{{ $product->metal_type }}</small>
                                            @endif
                                            @if ($product->stone_type)
                                                <small class="badge bg-light text-dark">{{ $product->stone_type }}</small>
                                            @endif
                                        </div>
                                    @endif

                                    <!-- Clear Add to Cart Button for All Devices -->
                                    <div class="mt-auto">
                                        @if($product->in_stock)
                                            <button class="btn btn-primary-pink w-100 mb-2 add-to-cart-btn"
                                                data-product-id="{{ $product->id }}"
                                                style="font-weight: 600;">
                                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                            </button>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-outline-secondary btn-sm flex-fill wishlist-btn"
                                                    data-product-id="{{ $product->id }}">
                                                    <i class="fas fa-heart me-1"></i>Wishlist
                                                </button>
                                                <a href="{{ route('product.detail', $product->slug) }}"
                                                   class="btn btn-outline-primary btn-sm flex-fill">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            </div>
                                        @else
                                            <button class="btn btn-secondary w-100 mb-2" disabled>
                                                <i class="fas fa-times me-2"></i>Out of Stock
                                            </button>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-outline-secondary btn-sm flex-fill wishlist-btn"
                                                    data-product-id="{{ $product->id }}">
                                                    <i class="fas fa-heart me-1"></i>Wishlist
                                                </button>
                                                <a href="{{ route('product.detail', $product->slug) }}"
                                                   class="btn btn-outline-primary btn-sm flex-fill">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>


                <!-- Pagination -->
                @if ($products->hasPages())
                    <div class="row mt-5">
                        <div class="col-12">
                            <nav aria-label="Products pagination">
                                {{ $products->appends(request()->query())->links('pagination::bootstrap-5') }}
                            </nav>
                        </div>
                    </div>
                @endif
            @else
                <!-- No Products Found -->
                <div class="row">
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-gem text-muted mb-4" style="font-size: 4rem;"></i>
                        <h3 class="font-modern mb-3">No Products Found</h3>
                        <p class="text-muted mb-4">
                            @if (isset($category))
                                No products found in {{ $category->name }} category.
                            @elseif(isset($searchTerm))
                                No products found for "{{ $searchTerm }}".
                            @else
                                No products found matching your criteria.
                            @endif
                        </p>
                        <a href="{{ route('collections') }}" class="btn btn-primary-pink">
                            <i class="fas fa-arrow-left me-2"></i>View All Products
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>
@endsection

@push('styles')
    <style>
        .breadcrumb-item+.breadcrumb-item::before {
            color: var(--primary-pink);
        }

        .breadcrumb-item a {
            color: var(--primary-pink);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            text-decoration: underline;
        }

        .pagination .page-link {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
            border-color: var(--primary-pink);
            color: white;
        }

        .pagination .page-link:hover {
            background: linear-gradient(135deg, var(--secondary-pink), var(--primary-pink));
            border-color: var(--secondary-pink);
            color: white;
        }

        /* Filter Section Styling */
        .btn-outline-pink {
            border-color: var(--primary-pink);
            color: var(--primary-pink);
            transition: all 0.3s ease;
        }

        .btn-outline-pink:hover {
            background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
            border-color: var(--primary-pink);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(233, 30, 99, 0.2);
        }

        .form-select:focus {
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 0.2rem rgba(233, 30, 99, 0.25);
        }

        .dropdown-menu {
            border: 1px solid var(--primary-pink);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(233, 30, 99, 0.15);
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, var(--light-pink), var(--secondary-pink));
            color: white;
        }

        /* Mobile Filter Improvements */
        @media (max-width: 767.98px) {
            .btn-outline-pink.btn-sm {
                font-size: 0.8rem;
                padding: 0.4rem 0.6rem;
            }

            .form-select.form-select-sm {
                font-size: 0.8rem;
                padding: 0.4rem 0.6rem;
            }
        }

        /* Desktop Filter Improvements */
        @media (min-width: 768px) {
            .btn-outline-pink {
                min-width: 120px;
                font-weight: 500;
            }

            .form-select {
                min-width: 180px;
                font-weight: 500;
            }
        }

        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
            background: #fff;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
        }

        .product-image-container {
            height: 250px;
            overflow: hidden;
            position: relative;
            background: #f8f9fa;
            border-radius: 12px 12px 0 0;
        }

        .card-img-top {
            height: 100%;
            width: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.05);
        }

        /* Hover hint styling */
        .hover-hint {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .product-card:hover .hover-hint {
            opacity: 0;
        }

        /* Removed product overlay - keeping only subtle hover effects */

        /* Mobile-First Collections Page Improvements */

        /* Mobile Header Adjustments */
        @media (max-width: 767.98px) {
            .display-5 {
                font-size: 2rem !important;
            }

            .breadcrumb {
                font-size: 0.85rem;
            }

            .breadcrumb-item+.breadcrumb-item::before {
                font-size: 0.75rem;
            }
        }

        /* Mobile Filter Section */
        .sticky-top {
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 767.98px) {
            .btn-sm {
                font-size: 0.8rem;
                padding: 0.375rem 0.5rem;
            }

            .form-select-sm {
                font-size: 0.8rem;
                padding: 0.375rem 0.5rem;
            }
        }

        /* Mobile Product Grid - Vertical Layout */
        @media (max-width: 575.98px) {
            .product-card {
                border-radius: 16px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
                overflow: hidden;
            }

            .product-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            }

            .product-image-container {
                height: auto !important;
                aspect-ratio: 1/1;
                min-height: 0;
            }
            .product-image-container img.card-img-top {
                width: 100% !important;
                height: 100% !important;
                object-fit: cover !important;
                display: block;
            }

            .card-body {
                padding: 1rem;
                text-align: center;
            }

            .card-title {
                font-size: 1.1rem;
                margin-bottom: 0.5rem;
                line-height: 1.3;
                font-weight: 600;
                color: #2c3e50;
            }

            .card-text {
                font-size: 0.85rem;
                margin-bottom: 0.75rem;
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .fs-5 {
                font-size: 1.25rem !important;
                font-weight: 700;
                color: var(--primary-pink);
            }

            .badge {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
                border-radius: 20px;
                margin: 0.125rem;
            }

            /* Mobile action buttons */
            .mobile-add-cart-btn,
            .buy-now-btn-mobile {
                font-size: 0.9rem;
                padding: 0.75rem 1rem;
                border-radius: 25px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .mobile-add-cart-btn {
                background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
                border: none;
                color: white;
            }

            .buy-now-btn-mobile {
                background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
                border: none;
                color: var(--text-dark);
            }

            /* Product overlay removed */

            /* Adjust spacing */
            .row.g-4 {
                --bs-gutter-y: 1.5rem;
            }

            /* Enhanced typography */
            .card-title {
                font-family: 'Playfair Display', serif;
                font-weight: 600;
                color: #2c3e50;
                line-height: 1.3;
            }

            .card-text {
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                font-weight: 500;
            }

            /* Enhanced price styling */
            .text-primary-brown {
                color: var(--primary-brown) !important;
                font-weight: 700;
            }

            /* Mobile-specific badge positioning */
            .badge.position-absolute {
                top: 1rem !important;
                left: 1rem !important;
                z-index: 2;
                font-weight: 600;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            /* Price section styling */
            .d-flex.justify-content-center.justify-content-sm-start {
                justify-content: center !important;
                margin-bottom: 1rem;
            }

            /* Badge container styling */
            .mt-auto {
                margin-top: 0.75rem !important;
            }

            /* Mobile button container */
            .d-flex.gap-2.mt-3.d-md-none {
                margin-top: 1rem !important;
                gap: 0.75rem !important;
            }

            /* Enhanced mobile typography */
            .font-modern {
                font-family: 'Poppins', 'Inter', sans-serif;
                font-weight: 600;
            }

            /* Better mobile card spacing */
            .container .row.g-3.g-md-4 {
                margin: 0 -0.5rem;
            }

            .container .row.g-3.g-md-4 > .col-12 {
                padding: 0 0.5rem;
            }
        }

        /* Tablet Product Grid */
        @media (min-width: 576px) and (max-width: 767.98px) {
            .product-card {
                border-radius: 12px;
                box-shadow: 0 3px 10px rgba(0,0,0,0.1);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .product-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            }

            .product-image-container {
                height: 220px;
                border-radius: 12px 12px 0 0;
                overflow: hidden;
            }

            .product-image-container img {
                transition: transform 0.3s ease;
            }

            .product-card:hover .product-image-container img {
                transform: scale(1.03);
            }

            .card-body {
                padding: 1rem;
            }

            .card-title {
                font-size: 1.05rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
            }

            .card-text {
                font-size: 0.9rem;
                margin-bottom: 0.75rem;
            }

            .fs-5 {
                font-size: 1.15rem !important;
                font-weight: 700;
            }
        }

        /* Overlay removed */

        /* Desktop Product Grid */
        @media (min-width: 768px) {
            .product-card {
                border-radius: 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
                transition: all 0.3s ease;
                overflow: hidden;
            }

            .product-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            }

            .product-image-container {
                height: 280px;
                border-radius: 12px 12px 0 0;
                overflow: hidden;
                position: relative;
            }

            .product-image-container img {
                transition: transform 0.4s ease;
            }

            .product-card:hover .product-image-container img {
                transform: scale(1.08);
            }

            .card-body {
                padding: 1.25rem;
            }

            .card-title {
                font-size: 1.15rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
                color: #2c3e50;
            }

            .card-text {
                font-size: 0.9rem;
                margin-bottom: 0.75rem;
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .fs-5 {
                font-size: 1.3rem !important;
                font-weight: 700;
                color: var(--primary-pink);
            }

            /* Removed desktop overlay styles */

            /* Badge styling for desktop */
            .badge.position-absolute {
                top: 1rem;
                left: 1rem;
                font-weight: 600;
                border-radius: 20px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            .mobile-add-cart-btn,
            .buy-now-btn-mobile {
                display: none !important;
            }
        }

        /* Mobile pagination */
        @media (max-width: 767.98px) {
            .pagination {
                justify-content: center;
            }

            .page-link {
                padding: 0.5rem 0.75rem;
                font-size: 0.9rem;
            }
        }

        /* Mobile empty state */
        @media (max-width: 767.98px) {
            .py-5 {
                padding: 2rem 0 !important;
            }

            .text-center h3 {
                font-size: 1.5rem;
            }

            .text-center p {
                font-size: 0.9rem;
            }
        }

        /* Touch-friendly improvements */
        @media (max-width: 767.98px) {

            .dropdown-toggle,
            .form-select,
            .btn {
                min-height: 44px;
            }

            .dropdown-item {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
        }

        /* Performance optimizations for mobile */
        @media (max-width: 767.98px) {
            .product-card .card-img-top {
                will-change: auto;
            }

            .product-card:hover .card-img-top {
                transform: none;
            }
        }

        /* Out of stock styling */
        .product-card.out-of-stock {
            position: relative;
        }

        .product-card.out-of-stock .card-body {
            opacity: 0.7;
        }

        /* Overlay removed */

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {

            .product-card,
            .product-card:hover {
                transform: none;
                transition: none;
            }
        }
    </style>
@endpush

@push('scripts')
    <script>
        // Add to cart functionality
        document.querySelectorAll('.add-to-cart-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;

                fetch('/cart/add', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: 1
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            showNotification('Product added to cart!', 'success');

                            // Update cart count if available
                            if (data.cart_count) {
                                updateCartCount(data.cart_count);
                            }
                        } else {
                            let errorMessage = data.message || 'Error adding product to cart';
                            if (errorMessage.includes('out of stock') || errorMessage.includes('insufficient')) {
                                errorMessage = 'Sorry, this product is currently out of stock or has insufficient quantity available.';
                            }
                            showNotification(errorMessage, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('Unable to add product to cart. Please check your connection and try again.', 'error');
                    });
            });
        });

        // Buy Now functionality
        document.querySelectorAll('.buy-now-btn, .buy-now-btn-mobile').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;

                // Add loading state
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
                this.disabled = true;

                fetch('/cart/add', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: 1,
                            buy_now: true
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Redirect to checkout
                            window.location.href = '/checkout';
                        } else {
                            // Restore button state
                            this.innerHTML = originalText;
                            this.disabled = false;
                            showNotification(data.message || 'Error processing request', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // Restore button state
                        this.innerHTML = originalText;
                        this.disabled = false;
                        showNotification('Error processing request', 'error');
                    });
            });
        });

        // Wishlist functionality
        document.querySelectorAll('.wishlist-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;

                fetch('/wishlist/toggle', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update heart icon
                            const icon = this.querySelector('i');
                            if (data.is_added) {
                                icon.classList.remove('far');
                                icon.classList.add('fas');
                                this.classList.remove('btn-light');
                                this.classList.add('btn-danger');
                            } else {
                                icon.classList.remove('fas');
                                icon.classList.add('far');
                                this.classList.remove('btn-danger');
                                this.classList.add('btn-light');
                            }

                            showNotification(data.message, 'success');
                        } else {
                            showNotification(data.message || 'Error updating wishlist', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('Please login to add items to wishlist', 'error');
                    });
            });
        });

        // Add to cart functionality for mobile buttons
        document.querySelectorAll('.add-to-cart-btn-mobile').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;

                fetch('/cart/add', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: 1
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification('Product added to cart!', 'success');
                            if (data.cart_count) {
                                updateCartCount(data.cart_count);
                            }
                        } else {
                            showNotification(data.message || 'Error adding product to cart', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('Error adding product to cart', 'error');
                    });
            });
        });

        // Utility functions
        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className =
                `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 350px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
            notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        function updateCartCount(count) {
            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = count;
            });
        }

        // New Add to Cart function for mobile buttons
        function addToCartMobile(productId) {
            fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to cart!', 'success');
                    if (data.cart_count) {
                        updateCartCount(data.cart_count);
                    }
                } else {
                    let errorMessage = data.message || 'Error adding product to cart';
                    if (errorMessage.includes('out of stock') || errorMessage.includes('insufficient')) {
                        errorMessage = 'Sorry, this product is currently out of stock or has insufficient quantity available.';
                    }
                    showNotification(errorMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Unable to add product to cart. Please check your connection and try again.', 'error');
            });
        }
    </script>
@endpush
